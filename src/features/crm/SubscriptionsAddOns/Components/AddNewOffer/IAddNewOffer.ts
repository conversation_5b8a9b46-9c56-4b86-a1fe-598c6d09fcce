import React from "react";
import { IAddOns, IOfferAddOns, IAddon } from "@services/subscription/accountId/interface/IAddOns";
import { ISubscriptionsCollection } from "@services/subscription/accountId/interface/ISubscriptionsAccountIds";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { IRenderComponentStep } from "@features/acquisition/PostpayAcquisition/Postpay/common/CustomerInformationStep/ICustomerInformationStep";
import { ITigoRiskManagementResponse } from "@modules/tigoSalesFacade/interfaces/responses/ITigoRiskManagementResponse";

export interface IPropsAddNewOffer {
    addons?: IAddOns | undefined;
    setAddNewAddOns: React.Dispatch<React.SetStateAction<boolean>>;
    subscriptions?: ISubscriptionsCollection | undefined;
    onClickCloseOffer?: () => void;
    callWfeFacade?: () => void;setSuccessScheduleInstallation: React.Dispatch<React.SetStateAction<boolean>>;
    setScheduleInstallation: React.Dispatch<React.SetStateAction<ISlot | undefined>>;
    setCorrelationId: React.Dispatch<React.SetStateAction<string | undefined>>;
    contactDetails: TContactDetails | undefined;
    successScheduleInstallation: boolean;
    setScheduleSuccess: React.Dispatch<React.SetStateAction<boolean>>;
    scheduleInstallation: ISlot | undefined;
    onClickConfirmSchedule: () => void;
    scheduleSuccess: boolean;
    setSuccessRisk: React.Dispatch<React.SetStateAction<ITigoRiskManagementResponse | undefined | null>>;
    setStepperRenderComponent: React.Dispatch<React.SetStateAction<IRenderComponentStep[] | undefined>>;
    correlationId: string | undefined;
    setComponentScheduleTitle: React.Dispatch<React.SetStateAction<string>>;
}

export interface IPropsUseAddNewOffer {
    addons?: IAddOns | undefined;
    subscriptions?: ISubscriptionsCollection | undefined;
    onClickCloseOffer?: () => void;
    callWfeFacade?: () => void;
    setSuccessRisk: React.Dispatch<React.SetStateAction<ITigoRiskManagementResponse | undefined | null>>;
    setAddNewAddOns: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface ITabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

export interface IAddedAddon {
    addon: IAddon;
    quantity: number;
    offer: IOfferAddOns;
}
