import { useState, useEffect } from "react";
import { Box, Button, Grid, Tab, Tabs, Typography } from "@mui/material";
import { useAddNewOffer } from "./useAddNewOffer";
import { IPropsAddNewOffer, ITabPanelProps } from "./IAddNewOffer";
import ShoppingCartOffer from "../ShoppingCartOffer/ShoppingCartOffer";
import OfferAddons from "./OfferAddons";
import { useTranslation } from "react-i18next";
import { OrderConfirmationModal } from "@common/styleComponents/OrderConfirmationModal/OrderConfirmationModal";
import ScheduleInstallation from "../../Views/ScheduleInstallation/ScheduleInstallation";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
// import { isEmpty } from "lodash";
import { useSnackBar } from "@common";
import { useStyle } from "../../style";

function CustomTabPanel(props: ITabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            aria-labelledby={`simple-tab-${index}`}
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            role="tabpanel"
            {...other}
        >
            {value === index && <Box sx={{ pt: 1 }}>{children}</Box>}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`,
    };
}

const AddNewOffer = ({
    subscriptions,
    onClickCloseOffer,
    setSuccessScheduleInstallation,
    setScheduleInstallation,
    contactDetails,
    setCorrelationId,
    correlationId,
    scheduleSuccess,
    setScheduleSuccess,
    setSuccessRisk,
    setAddNewAddOns,
    setComponentScheduleTitle,
}: IPropsAddNewOffer) => {
    const { t } = useTranslation(["customer"]);
    const { setSnackBarSuccess } = useSnackBar();

    const {
        handleChange,
        value,
        offers,
        subscriptionsActives,
        totalAddons,
        handleAddClick,
        handleRemoveClick,
        addedAddons,
        callWfeFacade,
        modalInfo,
        setModalInfo,
        handleRedirectAddAddon,
        serviceAddons,
    } = useAddNewOffer({
        subscriptions,
        setSuccessRisk,
        setAddNewAddOns,
    });

    const [showSchedule, setShowSchedule] = useState(false);
    const [hasScheduleAddon, setHasScheduleAddon] = useState(false);
    const [orderIdForSchedule, setOrderIdForSchedule] = useState<string>("");
    const { classes } = useStyle();

    useEffect(() => {
        const selected = addedAddons.some((addon) => {
            return addon.offer.code === "MATERIAL";
        });
        setHasScheduleAddon(selected);
    }, [addedAddons]);

    useEffect(() => {
        // Avanza automáticamente a agendar cuando el hook setea modalInfo con orderReferences
        if (hasScheduleAddon && modalInfo?.orderReferences?.length) {
            try {
                localStorage.removeItem("UNIQUE_CALL_ID");
            } catch {
                /* no-op */
            }

            // Pasar el orderId a la vista de agenda
            setOrderIdForSchedule(modalInfo.orderReferences[0]);

            // Navegación al flujo de cita (como hace closeModal)
            handleRedirectAddAddon(true);
            setShowSchedule(true);

            // Cerrar el modal automáticamente para que no quede encima
            setModalInfo(undefined);
        }
    }, [hasScheduleAddon, modalInfo?.orderReferences]);

    const handleConfirm = (scheduleData?: ISlot) => {
        if (hasScheduleAddon) {
            const filterSubscriptionsTest = subscriptions?.collection;
            callWfeFacade({
                scheduleParams: scheduleData,
                contactDetails: contactDetails,
                correlationId: correlationId ?? "",
                subscriptionsList: filterSubscriptionsTest ?? [],
                appointmentConfirmation: true,
            });
        } else {
            const filterSubscriptionsTest = subscriptions?.collection;
            callWfeFacade({
                scheduleParams: scheduleData,
                contactDetails: contactDetails,
                correlationId: correlationId ?? "",
                subscriptionsList: filterSubscriptionsTest ?? [],
            });
        }
    };

    const closeModal = () => {
        if (hasScheduleAddon) {
            setModalInfo(undefined);
            if (modalInfo?.orderReferences && modalInfo.orderReferences.length > 0) {
                setOrderIdForSchedule(modalInfo.orderReferences[0]);
            }
            handleRedirectAddAddon(true);
            setShowSchedule(true);
        } else {
            handleRedirectAddAddon(false);
            setShowSchedule(false);
        }
    };

    const STATUSES_THAT_BLOCK_ADD = new Set(["NO_ONGOING_REQUEST", "ONGOING_ADDITION"]);
    const getServiceAddonCode = (s: any) => s?.code as string | undefined;
    const serviceAddonsList: any[] = serviceAddons?.collection ?? [];

    const serviceAddonsCountByCode: Record<string, number> = serviceAddonsList.reduce((acc, item) => {
        const code = getServiceAddonCode(item);
        if (!code) return acc;
        const status = item?.status ?? "NO_ONGOING_REQUEST";
        if (STATUSES_THAT_BLOCK_ADD.has(status)) {
            acc[code] = (acc[code] ?? 0) + 1;
        }
        return acc;
    }, {} as Record<string, number>);

    const serviceGroupCountByOffer: Record<string, number> = {};
    offers.forEach((offer) => {
        const codesInGroup = new Set(offer.addons.map((a) => a.code));
        const count = serviceAddonsList.reduce((sum, item) => {
            const code = getServiceAddonCode(item);
            const status = item?.status ?? "NO_ONGOING_REQUEST";
            if (code && codesInGroup.has(code) && STATUSES_THAT_BLOCK_ADD.has(status)) {
                return sum + 1;
            }
            return sum;
        }, 0);
        serviceGroupCountByOffer[offer.code] = count;
    });

    const activeAddonsObj: Record<
        string,
        { activeQuantities: number; addonCode: string; maxQuantity: number; subscriptions: any[] }
    > = {};

    offers.forEach((offer) => {
        offer.addons.forEach((addon) => {
            const qtyInService = serviceAddonsCountByCode[addon.code] ?? 0;
            if (!activeAddonsObj[addon.code]) {
                activeAddonsObj[addon.code] = {
                    activeQuantities: 0,
                    addonCode: addon.code,
                    maxQuantity: addon.maxQuantity,
                    subscriptions: [],
                };
            }
            activeAddonsObj[addon.code].activeQuantities = qtyInService;
            activeAddonsObj[addon.code].subscriptions = serviceAddonsList.filter(
                (it) =>
                    getServiceAddonCode(it) === addon.code &&
                    STATUSES_THAT_BLOCK_ADD.has(it?.status ?? "NO_ONGOING_REQUEST")
            );
        });
    });

    // suma en carrito por grupo (lo que vas agregando ahora)
    const groupAddedCount = addedAddons.reduce<Record<string, number>>((acc, item) => {
        const g = item.offer.code;
        acc[g] = (acc[g] ?? 0) + (item.quantity ?? 0);
        return acc;
    }, {});
    useEffect(() => {
        if (scheduleSuccess) {
            setSnackBarSuccess(t("customer:scheduleSuccess"));
        }
    }, [scheduleSuccess, setSnackBarSuccess, t]);

    return (
        <div>
            {modalInfo && (
                <OrderConfirmationModal
                    isOpen
                    orderReference={modalInfo.orderReferences?.[0] ?? ""}
                    orderReferences={modalInfo.orderReferences}
                    onClose={closeModal}
                />
            )}
            {showSchedule ? (
                <ScheduleInstallation
                    contact={contactDetails}
                    scheduleSuccess={scheduleSuccess}
                    setCorrelationId={setCorrelationId}
                    setScheduleInstallation={setScheduleInstallation}
                    setSuccessScheduleInstallation={setSuccessScheduleInstallation}
                    onBackToAddNewOffer={() => {
                        onClickCloseOffer?.();
                        setScheduleSuccess(false);
                        setSuccessRisk(null);
                        setAddNewAddOns(false);
                        setScheduleInstallation({ finish: "", start: "", grade: 0 });
                        setShowSchedule(false);
                    }}
                    callIdFromOrder={orderIdForSchedule}
                />
            ) : (
                <Grid container direction="row" spacing={3}>
                    <Grid item md={6} xs={12}>
                        <Grid item>
                            <Typography
                                className={`${classes.titleAddOn} ${classes.subscriptionTitleContainer}`}            
                            >
                                {t("customer:addOnsTitle")}
                            </Typography>
                        </Grid>
                        <Tabs aria-label="add new offers" value={value} variant="fullWidth" onChange={handleChange}>
                            <Tab label="TV" {...a11yProps(0)} />
                            <Tab label="INTERNET" {...a11yProps(1)} />
                            <Tab label="MATERIAL" {...a11yProps(2)} />
                        </Tabs>
                        {/* TV */}
                        <CustomTabPanel index={0} value={value}>
                            <Grid container direction="row" spacing={2}>
                                {offers
                                    .filter((offer) => offer.code === "TV_CHANNEL")
                                    .map((offer) =>
                                        offer?.addons?.map((addon) => {
                                            const addedAddon = addedAddons.find(
                                                (item) => item.addon.code === addon.code
                                            );
                                            const currentActiveQty = activeAddonsObj[addon.code]?.activeQuantities ?? 0; // ← del endpoint /services/{id}/add-ons
                                            const quantity = addedAddon?.quantity ?? 0;

                                            // Límite grupo
                                            const groupMax = (offer as any).maxPerSubscription ?? 99999;
                                            const isUnlimitedGroup = groupMax >= 99999;
                                            const groupActive = serviceGroupCountByOffer[offer.code] ?? 0; // ← del endpoint /services/{id}/add-ons
                                            const groupAdded = groupAddedCount[offer.code] ?? 0; // ← carrito
                                            const groupRemainingNoCart = isUnlimitedGroup
                                                ? Number.POSITIVE_INFINITY
                                                : groupMax - groupActive;
                                            const groupRemainingWithCart = isUnlimitedGroup
                                                ? Number.POSITIVE_INFINITY
                                                : groupMax - groupActive - groupAdded;

                                            // Límite add-on
                                            const addonLimit = addon.maxQuantity ?? Number.MAX_SAFE_INTEGER;
                                            const addonRemainingNoCart = addonLimit - currentActiveQty; // lo ya existente
                                            const addonRemainingWithCart = addonLimit - currentActiveQty - quantity; // lo existente + carrito

                                            // Reglas UI
                                            const rowDisabled = groupRemainingNoCart <= 0 || addonRemainingNoCart <= 0; // fila gris si ya estaba al tope
                                            const addDisabled =
                                                groupRemainingWithCart <= 0 || addonRemainingWithCart <= 0; // “+” se apaga si con carrito llegas al tope

                                            // Tooltip
                                            let tooltip: string | undefined;
                                            if (
                                                !isUnlimitedGroup &&
                                                (groupRemainingWithCart <= 0 || groupRemainingNoCart <= 0)
                                            ) {
                                                const usedNow = Math.max(0, groupActive + groupAdded);
                                                tooltip = `Límite alcanzado para ${
                                                    (offer as any).displayName ?? offer.code
                                                } (${usedNow}/${groupMax}).`;
                                            } else if (addonRemainingWithCart <= 0 || addonRemainingNoCart <= 0) {
                                                tooltip = `Alcanzaste el máximo para ${addon.description} (${addonLimit}).`;
                                            }

                                            return (
                                                <OfferAddons
                                                    addon={addon}
                                                    disabled={rowDisabled}
                                                    addDisabled={addDisabled}
                                                    tooltip={tooltip}
                                                    handleAddClick={() =>
                                                        handleAddClick(addon, offer, currentActiveQty)
                                                    }
                                                    handleRemoveClick={() => handleRemoveClick(addon, offer)}
                                                    isAdded={quantity > 0}
                                                    key={addon.code}
                                                    quantity={quantity}
                                                />
                                            );
                                        })
                                    )}
                            </Grid>
                        </CustomTabPanel>

                        {/* INTERNET (todo lo que no es TV ni MATERIAL) */}
                        <CustomTabPanel index={1} value={value}>
                            <Grid container direction="row" spacing={2}>
                                {offers
                                    .filter((offer) => offer.code !== "TV_CHANNEL" && offer.code !== "MATERIAL")
                                    .map((offer) =>
                                        offer?.addons?.map((addon) => {
                                            const addedAddon = addedAddons.find(
                                                (item) => item.addon.code === addon.code
                                            );
                                            const currentActiveQty = activeAddonsObj[addon.code]?.activeQuantities ?? 0;
                                            const quantity = addedAddon?.quantity ?? 0;

                                            const groupMax = (offer as any).maxPerSubscription ?? 99999;
                                            const isUnlimitedGroup = groupMax >= 99999;
                                            const groupActive = serviceGroupCountByOffer[offer.code] ?? 0;
                                            const groupAdded = groupAddedCount[offer.code] ?? 0;
                                            const groupRemainingNoCart = isUnlimitedGroup
                                                ? Number.POSITIVE_INFINITY
                                                : groupMax - groupActive;
                                            const groupRemainingWithCart = isUnlimitedGroup
                                                ? Number.POSITIVE_INFINITY
                                                : groupMax - groupActive - groupAdded;

                                            const addonLimit = addon.maxQuantity ?? Number.MAX_SAFE_INTEGER;
                                            const addonRemainingNoCart = addonLimit - currentActiveQty;
                                            const addonRemainingWithCart = addonLimit - currentActiveQty - quantity;

                                            const rowDisabled = groupRemainingNoCart <= 0 || addonRemainingNoCart <= 0;
                                            const addDisabled =
                                                groupRemainingWithCart <= 0 || addonRemainingWithCart <= 0;

                                            let tooltip: string | undefined;
                                            if (
                                                !isUnlimitedGroup &&
                                                (groupRemainingWithCart <= 0 || groupRemainingNoCart <= 0)
                                            ) {
                                                const usedNow = Math.max(0, groupActive + groupAdded);
                                                tooltip = `Límite alcanzado para ${
                                                    (offer as any).displayName ?? offer.code
                                                } (${usedNow}/${groupMax}).`;
                                            } else if (addonRemainingWithCart <= 0 || addonRemainingNoCart <= 0) {
                                                tooltip = `Alcanzaste el máximo para ${addon.description} (${addonLimit}).`;
                                            }

                                            return (
                                                <OfferAddons
                                                    addon={addon}
                                                    disabled={rowDisabled}
                                                    addDisabled={addDisabled}
                                                    tooltip={tooltip}
                                                    handleAddClick={() =>
                                                        handleAddClick(addon, offer, currentActiveQty)
                                                    }
                                                    handleRemoveClick={() => handleRemoveClick(addon, offer)}
                                                    isAdded={quantity > 0}
                                                    key={addon.code}
                                                    quantity={quantity}
                                                />
                                            );
                                        })
                                    )}
                            </Grid>
                        </CustomTabPanel>

                        {/* MATERIAL */}
                        <CustomTabPanel index={2} value={value}>
                            <Grid container direction="row" spacing={2}>
                                {offers
                                    .filter((offer) => offer.code === "MATERIAL")
                                    .map((offer) =>
                                        offer?.addons?.map((addon) => {
                                            const addedAddon = addedAddons.find(
                                                (item) => item.addon.code === addon.code
                                            );
                                            const currentActiveQty = activeAddonsObj[addon.code]?.activeQuantities ?? 0;
                                            const quantity = addedAddon?.quantity ?? 0;

                                            const groupMax = (offer as any).maxPerSubscription ?? 99999;
                                            const isUnlimitedGroup = groupMax >= 99999;
                                            const groupActive = serviceGroupCountByOffer[offer.code] ?? 0;
                                            const groupAdded = groupAddedCount[offer.code] ?? 0;
                                            const groupRemainingNoCart = isUnlimitedGroup
                                                ? Number.POSITIVE_INFINITY
                                                : groupMax - groupActive;
                                            const groupRemainingWithCart = isUnlimitedGroup
                                                ? Number.POSITIVE_INFINITY
                                                : groupMax - groupActive - groupAdded;

                                            const addonLimit = addon.maxQuantity ?? Number.MAX_SAFE_INTEGER;
                                            const addonRemainingNoCart = addonLimit - currentActiveQty;
                                            const addonRemainingWithCart = addonLimit - currentActiveQty - quantity;

                                            const rowDisabled = groupRemainingNoCart <= 0 || addonRemainingNoCart <= 0;
                                            const addDisabled =
                                                groupRemainingWithCart <= 0 || addonRemainingWithCart <= 0;

                                            let tooltip: string | undefined;
                                            if (
                                                !isUnlimitedGroup &&
                                                (groupRemainingWithCart <= 0 || groupRemainingNoCart <= 0)
                                            ) {
                                                const usedNow = Math.max(0, groupActive + groupAdded);
                                                tooltip = `Límite alcanzado para ${
                                                    (offer as any).displayName ?? offer.code
                                                } (${usedNow}/${groupMax}).`;
                                            } else if (addonRemainingWithCart <= 0 || addonRemainingNoCart <= 0) {
                                                tooltip = `Alcanzaste el máximo para ${addon.description} (${addonLimit}).`;
                                            }

                                            return (
                                                <OfferAddons
                                                    addon={addon}
                                                    disabled={rowDisabled}
                                                    addDisabled={addDisabled}
                                                    tooltip={tooltip}
                                                    handleAddClick={() =>
                                                        handleAddClick(addon, offer, currentActiveQty)
                                                    }
                                                    handleRemoveClick={() => handleRemoveClick(addon, offer)}
                                                    isAdded={quantity > 0}
                                                    key={addon.code}
                                                    quantity={quantity}
                                                />
                                            );
                                        })
                                    )}
                            </Grid>
                        </CustomTabPanel>
                    </Grid>
                    <Grid item md={6} xs={12}>
                        <ShoppingCartOffer addedAddons={addedAddons} totalAddons={totalAddons} />
                        <Box sx={{ py: 1, px: { xs: 2, sm: 4 } }}>
                            <Grid container spacing={2}>
                                <Grid item xs={6}>
                                    <Button
                                        color="inherit"
                                        fullWidth
                                        variant="contained"
                                        onClick={() => {
                                            onClickCloseOffer?.();
                                            setAddNewAddOns(false);
                                            setSuccessRisk(null);
                                        }}
                                    >
                                        {t("customer:cancelSubscriptionAddOns")}
                                    </Button>
                                </Grid>
                                <Grid item xs={6}>
                                    <Button
                                        disabled={!addedAddons.length}
                                        fullWidth
                                        sx={{ mb: 2 }}
                                        variant="contained"
                                        onClick={() => handleConfirm()}
                                    >
                                        {hasScheduleAddon ? t("customer:continue") : t("customer:confirm")}
                                    </Button>
                                </Grid>
                            </Grid>
                        </Box>
                    </Grid>
                </Grid>
            )}
            {false && (
                <Grid container spacing={2}>
                    <Grid item xs={6}>
                        <Button
                            color="inherit"
                            fullWidth
                            sx={{ mt: 2 }}
                            variant="contained"
                            onClick={() => {
                                onClickCloseOffer?.();
                                setScheduleSuccess(false);
                                setSuccessRisk(null);
                                setAddNewAddOns(false);
                                setScheduleInstallation({
                                    finish: "",
                                    start: "",
                                    grade: 0,
                                });
                            }}
                        >
                            {t("customer:cancelSubscriptionAddOns")}
                        </Button>
                    </Grid>
                </Grid>
            )}
        </div>
    );
};

export default AddNewOffer;
