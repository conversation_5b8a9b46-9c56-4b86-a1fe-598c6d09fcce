import { useEffect, useState } from "react";
import { STORAGE_KEY_SELECTED_CHANNEL } from "@constants";
import { useFetchState } from "@hooks/useFetchState";
import {
    getAccountSubscription,
    getOffersAddons,
    getActiveServices,
    getUniqueService,
    getAddOnsForService,
} from "@services/subscription/accountId/api/getSubscriptionsAccountIds";
import { IAddedAddon, IPropsUseAddNewOffer } from "./IAddNewOffer";
import { IOfferAddOns, IAddon } from "@services/subscription/accountId/interface/IAddOns";
import { ICollectionActiveServices } from "@services/subscription/accountId/interface/IActiveServices";
import { createOrder } from "@modules/wfeOrderFacade/apis/v0";
import { EWFEOrderFacadeUseCase } from "@modules/wfeOrderFacade/constants";
import { IModalInfo } from "@features/acquisition/PostpayAcquisition/Postpay/common/DepositUpfrontStep/IDepositUpfront";
import { IAddOn, TCreateOrderPayload } from "@modules/wfeOrderFacade/interfaces/payloads/ICreateOrderPayload";
import { useSnackBar } from "@common";
import { getErrorToDisplay } from "itsf-ui-common";
import { ISlot } from "@modules/tigoSalesFacade/interfaces/responses/ITigoTimeIntervalResponse";
import { IAccountSubscription } from "@services/subscription/accountId/interface/ISubscriptionsForId";
import { TContactDetails } from "@features/crm/CustomerDetails/ICustomerDetails";
import { useTranslation } from "react-i18next";
import {
    registerAddOnsAndCollectRequestIds,
    ISelectedAddOnItem,
} from "@modules/tigoSalesFacade/apis/v1/suspendSubscriptions";

export const useAddNewOffer = ({ subscriptions, setSuccessRisk, setAddNewAddOns }: IPropsUseAddNewOffer) => {
    const { startFetching, endFetching, endFetchingError } = useFetchState();
    const [value, setValue] = useState(0);
    const { t } = useTranslation(["customer"]);
    const [offers, setOffers] = useState<IOfferAddOns[]>([]);
    const [serviceAddons, setServiceAddons] = useState<IAddOns>({ collection: [] });
    const [subscriptionsActives, setSubscriptionsActives] = useState<ICollectionActiveServices[]>([]);
    const [addedAddons, setAddedAddons] = useState<IAddedAddon[]>([]);
    const [totalAddons, setTotalAddons] = useState<number>(0);
    const [subscriptionAccount, setSubscriptionAccount] = useState<IAccountSubscription[]>([]);
    const { setSnackBarError, setSnackBarSuccess } = useSnackBar();
    const [orderIdForSchedule, setOrderIdForSchedule] = useState<string>("");

    const loadServiceAddons = async (serviceId: string) => {
        try {
            const resp = await getAddOnsForService(serviceId);
            setServiceAddons((prev) => {
                if (!prev || prev.collection.length === 0) return resp;

                const combined = [...prev.collection, ...resp.collection];

                // Dedupe robusto: usa id si existe; si no, usa code|serviceId
                const uniqueByKey = new Map<string, (typeof resp.collection)[number]>();
                for (const item of combined) {
                    const key = item.id != null ? `id:${item.id}` : `code:${item.code}|svc:${item.serviceId}`;
                    if (!uniqueByKey.has(key)) uniqueByKey.set(key, item);
                }

                return { ...resp, collection: Array.from(uniqueByKey.values()) };
            });
        } catch {
            // opcional: mostrar snackbar o ignorar silenciosamente
        }
    };

    const handleAddClick = (addon: IAddon, offer: IOfferAddOns, currentActiveQty: number) => {
        setAddedAddons((prev) => {
            const existingAddon = prev.find((item) => item.addon.code === addon.code);
            const alreadyAdded = existingAddon ? existingAddon.quantity : 0;

            const totalUsed = currentActiveQty + alreadyAdded;

            if (totalUsed >= addon.maxQuantity) {
                setSnackBarSuccess(t("customer:isLimitReached", { totalUsed, maxQuantity: addon.maxQuantity }));

                return prev;
            }

            if (existingAddon) {
                return prev.map((item) =>
                    item.addon.code === addon.code ? { ...item, quantity: item.quantity + 1 } : item
                );
            }

            return [...prev, { addon, quantity: 1, offer }];
        });
    };

    const handleRemoveClick = (addon: IAddon, offer: IOfferAddOns) => {
        setAddedAddons((prev) => {
            const existingAddon = prev.find((item) => item.addon.code === addon.code);

            if (!existingAddon) {
                return prev;
            }

            if (existingAddon.quantity > 1) {
                return prev.map((item) =>
                    item.addon.code === addon.code ? { ...item, offer, quantity: item.quantity - 1 } : item
                );
            }

            return prev.filter((item) => item.addon.code !== addon.code);
        });
    };

    const [modalInfo, setModalInfo] = useState<IModalInfo & { orderReferences?: string[] }>();

    const servicesActives = async (_serviceId: string) => {
        try {
            startFetching();
            const response = await getActiveServices(_serviceId, {});
            setSubscriptionsActives((prev) => [...prev, ...response.collection]);
            endFetching();
        } catch (error) {
            endFetchingError(error);
        }
    };

    const callWfeFacade = async ({
        scheduleParams,
        contactDetails,
        subscriptionsList,
        appointmentConfirmation,
    }: {
        scheduleParams: ISlot | undefined;
        contactDetails: TContactDetails | undefined;
        correlationId?: string;
        subscriptionsList: any[];
        appointmentConfirmation?: boolean;
    }) => {
        const orderReferences: string[] = [];
        let lastOrderId: string | undefined;

        function getAppointmentInfo(start?: string, finish?: string) {
            if (!start || !finish) return {};
            const startDate = new Date(start);
            const finishDate = new Date(finish);
            const appointmentDate = startDate.toISOString().split("T")[0];
            const startHour = startDate.getUTCHours();
            const finishHour = finishDate.getUTCHours();
            const startPeriod = startHour >= 12 ? "PM" : "AM";
            const finishPeriod = finishHour >= 12 ? "PM" : "AM";
            const appointmentTimeSlot = startPeriod === "PM" && finishPeriod === "PM" ? "PM" : "AM";
            return { appointmentDate, appointmentTimeSlot };
        }

        const appointmentInfo = getAppointmentInfo(scheduleParams?.start, scheduleParams?.finish);

        const offerCodeToServiceGroup: Record<string, string> = {
            TV_CHANNEL: "TV",
            OTT: "INTERNET",
            BB_ADDON: "INTERNET",
            MATERIAL: "TV",
            ADDON_VIX: "INTERNET",
        };

        const addonsByServiceGroup: Record<string, IAddedAddon[]> = {};
        addedAddons.forEach((item) => {
            const group = offerCodeToServiceGroup[item.offer.code];
            if (!group) return;
            if (!addonsByServiceGroup[group]) addonsByServiceGroup[group] = [];
            addonsByServiceGroup[group].push(item);
        });

        console.log("#### subscriptionsList", subscriptionsList);

        const hasAnyAddons = subscriptionsList.some(({ serviceGroup }) => {
            const addonsForService = addonsByServiceGroup[serviceGroup];
            return addonsForService && addonsForService.length > 0;
        });

        if (!hasAnyAddons) {
            setSnackBarError("No hay add-ons seleccionados para las suscripciones disponibles.");
            return;
        }

        type RegisteredByService = {
            serviceId: string | number;
            addOnList: Array<{
                catalogCode: string;
                itemGroupCode: string;
                description?: string;
                requestId: number;
                quantity: number;
            }>;
        };

        const phase1Results: RegisteredByService[] = [];

        // ---------- Fase 1: registrar add-ons y recolectar requestId ----------
        try {
            startFetching();
            console.log("1");
            for (const subscription of subscriptionsList) {
                const { id: serviceId, serviceGroup } = subscription;
                const addonsForService = addonsByServiceGroup[serviceGroup];
                if (!addonsForService || !addonsForService.length) continue;
                console.log("2");
                const itemsForRegistration: ISelectedAddOnItem[] = addonsForService.flatMap((item) => {
                    const qty = Number(item.quantity) || 1;

                    const base: ISelectedAddOnItem = {
                        catalogCode: item.addon.code,
                        itemGroupCode: item.offer.code,
                        description: item.addon.description,
                        ...(item.offer.code === "MATERIAL" && contactDetails?.contactUuid
                            ? { deliveryContactUuid: String(contactDetails.contactUuid) }
                            : {}),
                        ...(item?.addon?.frequency ? { frequency: item.addon.frequency } : {}),
                    };

                    return Array.from({ length: qty }, () => base);
                });
                console.log("3");
                const missingDelivery = itemsForRegistration.find(
                    (it) => it.itemGroupCode === "MATERIAL" && !it.deliveryContactUuid
                );
                if (missingDelivery)
                    throw new Error(`Falta deliveryContactUuid para ${missingDelivery.catalogCode} (MATERIAL).`);
                console.log("4");
                const addOnList = await registerAddOnsAndCollectRequestIds(serviceId, itemsForRegistration);
                phase1Results.push({ serviceId, addOnList });
                console.log("5");
            }
        } catch (err: any) {
            setSnackBarError(getErrorToDisplay(err));
            endFetching();
            return;
        } finally {
            endFetching();
        }

        if (!phase1Results.length) {
            setSnackBarError("No hay add-ons seleccionados para las suscripciones disponibles.");
            return;
        }

        // ---------- Fase 2: ÚNICA orden con services[] (esquema WFE) ----------
        try {
            startFetching();

            let orderIdReference: string | undefined;
            try {
                const { reference } = await getUniqueService();
                orderIdReference = reference;
            } catch {
                throw new Error("No se pudo obtener el orderIdReference.");
            }

            const servicesPayload = phase1Results.map(({ serviceId, addOnList }) => ({
                serviceId: String(serviceId ?? ""),
                addOn: addOnList.map((a) => ({
                    catalogCode: a.catalogCode,
                    itemGroupCode: a.itemGroupCode,
                    description: a.description,
                    quantity: a.quantity,
                    requestId: a.requestId,
                })),
            }));

            const hasMaterial = phase1Results.some(({ addOnList }) =>
                addOnList.some((a) => a.itemGroupCode === "MATERIAL")
            );

            const orderPayload: any = {
                orderId: orderIdReference,
                requestorUsername: contactDetails?.emails?.[0]?.email,
                useCase: EWFEOrderFacadeUseCase.ADD_ADDONS,
                appointmentConfirmation: appointmentConfirmation ?? false,
                services: servicesPayload,
                ...(hasMaterial ? appointmentInfo : {}),
            };

            const response = await createOrder(orderPayload as unknown as TCreateOrderPayload, true);
            orderReferences.push(response.orderId);
            lastOrderId = response.orderId;
        } catch (err: any) {
            setSnackBarError(getErrorToDisplay(err));
            return;
        } finally {
            endFetching();
        }

        if (orderReferences.length) {
            setOrderIdForSchedule(orderReferences[0]);
            setModalInfo({
                orderReferences,
                onClick: () => handleRedirectAddAddon(appointmentConfirmation ?? false),
                orderReference: orderReferences[0],
            });
        } else {
            setSnackBarError("No se pudo crear la orden para los add-ons seleccionados.");
        }
    };

    const callWfeFacadeCancelationAddon = async ({
        contactDetails,
        appointmentConfirmation,
        addonId,
        serviceId,
        setOrderIdForSchedule,
    }: {
        contactDetails: TContactDetails | undefined;
        appointmentConfirmation?: boolean;
        addonId: number;
        serviceId: number;
        setOrderIdForSchedule: React.Dispatch<React.SetStateAction<string>>;
    }) => {
        const orderReferences: string[] = [];
        let lastOrderId: string | undefined = undefined;

        // Para cada suscripción, si tiene addons, crear orden
        let atLeastOneOrder = false;

        const order: TCreateOrderPayload = {
            //orderId: orderIdReference,
            requestorUsername: contactDetails?.emails[0].email,
            useCase: EWFEOrderFacadeUseCase.REMOVE_ADDONS,
            serviceId: serviceId,
            appointmentConfirmation: appointmentConfirmation ?? false,
            addOn: [
                {
                    id: addonId,
                },
            ] as unknown as IAddOn[],
        };
        try {
            startFetching();
            const response = await createOrder(order, true);
            orderReferences.push(response.orderId);
            lastOrderId = response.orderId;
            setOrderIdForSchedule(response.orderId);
        } catch (error) {
            setSnackBarError(getErrorToDisplay(error));
        } finally {
            endFetching();
        }
    };

    const handleRedirectAddAddon = (appointmentConfirmation?: boolean) => {
        setSuccessRisk(null);
        if (!appointmentConfirmation) {
            setAddNewAddOns(false);
        }
    };

    useEffect(() => {
        if (addedAddons.length) {
            const total = addedAddons.reduce((acc, curr) => {
                const addonPrice = curr.addon.prices.reduce((priceAcc, price) => priceAcc + price.price, 0);

                return acc + addonPrice * curr.quantity;
            }, 0);
            setTotalAddons(total);
        }
    }, [addedAddons]);

    const selectedChannel = localStorage.getItem(STORAGE_KEY_SELECTED_CHANNEL);
    const handleChange = (_: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const OfferAddOns = async ({ channelCode, serviceId }: { channelCode: string; serviceId: string }) => {
        try {
            startFetching();
            const response = await getOffersAddons(channelCode, serviceId);
            setOffers((prev: IOfferAddOns[]) => {
                const updatedOffers = [...prev];

                response.collection.forEach((incomingOffer: IOfferAddOns) => {
                    const existingIndex = updatedOffers.findIndex((offer) => offer.code === incomingOffer.code);
                    // if no exsting index only push in array offer
                    if (existingIndex === -1) {
                        updatedOffers.push({
                            ...incomingOffer,
                            addons: [...incomingOffer.addons],
                        });
                    } else {
                        // if no existing index
                        const existingOffer = updatedOffers[existingIndex];

                        // create a set of existing addon codes
                        const existingAddonCodes = new Set(existingOffer.addons.map((a) => a.code));

                        //  filtering new addons that are not in existing addons
                        const newAddons = incomingOffer.addons.filter((a) => !existingAddonCodes.has(a.code));

                        if (newAddons.length > 0) {
                            updatedOffers[existingIndex] = {
                                ...existingOffer,
                                addons: [...existingOffer.addons, ...newAddons],
                            };
                        }
                    }
                });

                return updatedOffers;
            });
            endFetching();
        } catch (error) {
            //  endFetchingError(error);
        }
    };

    const serviceAccountSubscription = async (subscriptionId: string) => {
        try {
            startFetching();
            const response = await getAccountSubscription(subscriptionId);

            setSubscriptionAccount(response);
            endFetching();
        } catch (error) {
            endFetchingError(error);
        }
    };

    useEffect(() => {
        const fetchSubscriptions = async () => {
            if (subscriptions?.collection.length) {
                for (const subscription of subscriptions.collection) {
                    await serviceAccountSubscription(subscription.id.toString());
                }
            }
        };

        fetchSubscriptions();
    }, [subscriptions]);

    useEffect(() => {
        const channelCode = selectedChannel && JSON.parse(selectedChannel)?.channelGroup;
        if (subscriptionAccount.length) {
            subscriptionAccount.forEach(({ id }) => {
                const svcId = id.toString();

                OfferAddOns({ channelCode, serviceId: svcId });
                servicesActives(svcId);
                loadServiceAddons(svcId);
            });
        }
    }, [subscriptionAccount]);

    return {
        value,
        handleChange,
        offers,
        addedAddons,
        handleAddClick,
        handleRemoveClick,
        subscriptionsActives,
        totalAddons,
        callWfeFacade,
        modalInfo,
        handleRedirectAddAddon,
        setModalInfo,
        orderIdForSchedule,
        setOrderIdForSchedule,
        callWfeFacadeCancelationAddon,
        serviceAddons,
    };
};
